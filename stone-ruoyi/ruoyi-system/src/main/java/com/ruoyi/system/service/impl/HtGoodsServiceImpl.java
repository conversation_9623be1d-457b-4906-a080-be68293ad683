package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.RedisLockUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.constants.BusinessConstants;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.IHtGoodsService;
import com.ruoyi.system.utils.CreateOrderUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
@Slf4j
@Service
public class HtGoodsServiceImpl extends ServiceImpl<HtGoodsMapper, HtGoods> implements IHtGoodsService {
    @Autowired
    private HtGoodsMapper htGoodsMapper;
    @Autowired
    private HtOrderMapper htOrderMapper;
    @Autowired
    private CreateOrderUtils createOrderUtils;
    @Autowired
    private HtPaymentAgreementsMapper htPaymentAgreementsMapper;
    @Autowired
    private HtOrderRecordMapper htOrderRecordMapper;
    @Autowired
    private HtGoodsMapper goodsMapper;
    @Autowired
    private RedisLockUtils redisLockUtils;
    @Autowired
    private ComputingConfigUtils computingConfigUtils;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private org.springframework.data.redis.core.RedisTemplate redisTemplate;

    /**
     * 查询商品
     *
     * @param id 商品主键
     * @return 商品
     */
    @Override
    public HtGoods selectHtGoodsById(Long id) {
        return htGoodsMapper.selectHtGoodsById(id);
    }

    /**
     * 查询商品列表
     *
     * @param htGoods 商品
     * @return 商品
     */
    @Override
    public List<HtGoods> selectHtGoodsList(HtGoods htGoods) {
        if (htGoods.getIsBest() != null) {
            if (htGoods.getIsBest() != 0) {
                htGoods.setIsShow(1);
            }
        }
        if (htGoods.getIsNew() != null) {
            if (htGoods.getIsNew() != 0) {
                htGoods.setIsShow(1);
            }
        }
        if (htGoods.getActivityId() != null) {
            htGoods.setIsShow(1);
        }
        if (htGoods.getType() == null) {
            htGoods.setType(1);
        }
        List<HtGoods> list = htGoodsMapper.selectHtGoodsList(htGoods);
        if (htGoods.getType() == 1) {
            list.sort(Comparator.comparing(HtGoods::getPrice).reversed());
        } else {
            list.sort(Comparator.comparing(HtGoods::getPrice));
        }

        return list;
    }

    /**
     * 新增商品
     *
     * @param htGoods 商品
     * @return 结果
     */
    @Override
    public int insertHtGoods(HtGoods htGoods) {
        BigDecimal price = htGoods.getPrice();
        HtFeeRevenueConfig htFeeRevenueConfig = computingConfigUtils.getHtFeeRevenueConfig();
        // 售价 1w    转售价 = 售价 + 5%
        htGoods.setResalePrice(price.multiply((new BigDecimal("1").add(htFeeRevenueConfig.getFeeAmount()))).setScale(0, RoundingMode.HALF_UP));
        htGoods.setCreateTime(DateUtils.getNowDate());
        return htGoodsMapper.insertHtGoods(htGoods);
    }

    /**
     * 修改商品
     *
     * @param htGoods 商品
     * @return 结果
     */
    @Override
    public int updateHtGoods(HtGoods htGoods) {
        BigDecimal price = htGoods.getPrice();
        HtFeeRevenueConfig htFeeRevenueConfig = computingConfigUtils.getHtFeeRevenueConfig();
        // 售价 1w    转售价 = 售价 + 5%
        htGoods.setResalePrice(price.multiply((new BigDecimal("1").add(htFeeRevenueConfig.getFeeAmount()))).setScale(0, RoundingMode.HALF_UP));
        htGoods.setUpdateTime(DateUtils.getNowDate());
        return htGoodsMapper.updateHtGoods(htGoods);
    }

    /**
     * 批量删除商品
     *
     * @param ids 需要删除的商品主键
     * @return 结果
     */
    @Override
    public int deleteHtGoodsByIds(Long[] ids) {
        return htGoodsMapper.deleteHtGoodsByIds(ids);
    }

    /**
     * 删除商品信息
     *
     * @param id 商品主键
     * @return 结果
     */
    @Override
    public int deleteHtGoodsById(Long id) {
        return htGoodsMapper.deleteHtGoodsById(id);
    }

    @Override
    public List<HtGoods> selectSystemHtGoodsList(HtGoods htGoods) {
        List<HtGoods> list = htGoodsMapper.selectSystemHtGoodsList(htGoods);

        return list;
    }

    @Override
    public int insertSystemHtGoods(HtGoods htGoods) {
        htGoods.setUserId(SecurityUtils.getUserId().intValue());
        BigDecimal price = htGoods.getPrice();
        htGoods.setActivityId(5L);

        HtFeeRevenueConfig htFeeRevenueConfig = computingConfigUtils.getHtFeeRevenueConfig();
        // 售价 1w    转售价 = 售价 + 5%
        htGoods.setResalePrice(price.multiply((new BigDecimal("1").add(htFeeRevenueConfig.getFeeAmount()))).setScale(0, RoundingMode.HALF_UP));
        htGoods.setCreateTime(DateUtils.getNowDate());
        return htGoodsMapper.insertHtGoods(htGoods);
    }



    @Override
    public List<HtGoods> selectMyHtGoodsList(Long userId) {
        List<HtGoods> list = htGoodsMapper.selectMyHtGoodsList(userId);
        return list;
    }

    /**
     * 创建订单对象
     *
     * @param userId 用户ID
     * @param htGoods 商品对象
     * @return 订单对象
     */
    public HtOrder getOrder(Integer userId, HtGoods htGoods) {
        // 参数校验
        if (StringUtils.isNull(userId) || StringUtils.isNull(htGoods)) {
            log.error("创建订单失败：参数不能为空, userId: {}, htGoods: {}", userId, htGoods);
            return null;
        }
        try {
            // 1. 创建基础订单信息
            HtOrder htOrder =createOrderUtils.createBaseOrder(userId, htGoods);

            // 2. 设置收款人信息
            createOrderUtils.setRecipientInfo(htOrder);

            return htOrder;
        } catch (Exception e) {
            log.error("创建订单异常, userId: {}, goodsId: {}", userId, htGoods.getId(), e);
            return null;
        }
    }

    /**
     * 获取用户商品列表
     */
    private List<HtGoods> getUserGoodsList(Integer userId) {
        return goodsMapper.selectList(Wrappers.<HtGoods>query()
                .eq("user_id", userId)
                .eq("is_del", BusinessConstants.GOODS_NOT_DELETED));
    }

    /**
     * 商品抢购
     *
     * @param userId 用户主键
     * @param goodsId 商品主键
     * @return 结果
     */
    @Override
    public AjaxResult snappedGoodsOptimized(Integer userId, Integer goodsId) {
        // 参数校验
        if (StringUtils.isNull(userId) || StringUtils.isNull(goodsId) ) {
            return AjaxResult.error("参数不能为空");
        }
        // 生成唯一的请求标识，用于锁的持有者验证
        String requestId = UUID.randomUUID().toString();
        String lockKey = "GOODS_SNAP_" + goodsId;

        try {
            // 1. 获取分布式锁，设置超时时间防止死锁
            boolean locked = redisLockUtils.lockWithTimeout(lockKey, requestId, 60);
            if (!locked) {
                return AjaxResult.error("当前商品抢购人数过多，请稍后再试");
            }
            // 2. 预检查：用户今日抢购限制（使用Redis缓存）
            String userDailyKey = "USER_DAILY_SNAP_" + userId + "_" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
            Integer dailySnapCount = redisCache.getCacheObject(userDailyKey);
            if (StringUtils.isNotNull(dailySnapCount) && dailySnapCount >= 2) {
                return AjaxResult.warn("您今天已经抢购了2次，无法再次抢购");
            }
            // 3. 预检查：时间窗口验证（缓存配置信息）
            if (!isInSnappingTime()) {
                return AjaxResult.error( "还未到抢购时间");
            }
            // 4. 检查当前用户有未上架的商品，则无法参与抢购
            Long selectCountNotShow=goodsMapper.selectGoodsByUserIdNotShow(Long.valueOf(userId));
            if (selectCountNotShow>0) {
                return AjaxResult.warn("无法参与抢购");
            }
            // 5. 商品状态检查和原子性更新
            AjaxResult result = performSnapping(userId, goodsId);
            if (result.isError()) {
                return result;
            }
            // 6. 更新用户今日抢购次数
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime midnight = now.toLocalDate().plusDays(1).atStartOfDay();
            Integer secondsUntilMidnight = Math.toIntExact(ChronoUnit.SECONDS.between(now, midnight));
            Integer currentCount = redisCache.getCacheObject(userDailyKey);
            if (currentCount == null) {
                currentCount = 0;
            }
            redisCache.setCacheObject(userDailyKey, currentCount + 1, secondsUntilMidnight, TimeUnit.SECONDS);
            return AjaxResult.success("抢购成功");
        } catch (Exception e) {
            log.error("抢购商品异常，userId: {}, goodsId: {}", userId, goodsId, e);
            return AjaxResult.error("系统异常，请稍后重试");
        } finally {
            // 释放锁
            redisLockUtils.unlock(lockKey);
        }
    }

    /**
     * 检查是否在抢购时间窗口内
     */
    private boolean isInSnappingTime() {
        // 从缓存获取配置，避免每次数据库查询
        String configKey = "SNAPPING_TIME_CONFIG";
        HtFeeRevenueConfig config = redisCache.getCacheObject(configKey);
        if (config == null) {
            config = computingConfigUtils.getHtFeeRevenueConfig();
            if (config != null) {
                redisCache.setCacheObject(configKey, config, 1, TimeUnit.HOURS);
            }
        }
        LocalTime currentTime = LocalTime.now();
        LocalTime startTime = LocalTime.of(9, 10); // 默认值
        LocalTime endTime = LocalTime.of(9, 20);   // 默认值

        if (config != null && config.getStartTime() != null && config.getEndTime() != null) {
            startTime = LocalTime.parse(config.getStartTime());
            endTime = LocalTime.parse(config.getEndTime());
        }

        return currentTime.isAfter(endTime) || currentTime.isBefore(startTime);
    }

    /**
     * 执行抢购核心逻辑
     */
    private AjaxResult performSnapping(Integer userId, Integer goodsId) {
        // 1. 查询商品信息
        HtGoods htGoods = htGoodsMapper.selectById(goodsId);
        if (StringUtils.isNull(htGoods)) {
            return AjaxResult.error("商品不存在");
        }
        if (htGoods.getIsShow() == 0) {
            return AjaxResult.error("商品已抢完");
        }
        // 2. 原子性更新商品状态
        HtGoods updateGoods = new HtGoods();
        updateGoods.setIsShow(0);
        updateGoods.setId(Long.valueOf(goodsId));
        int updateCount = htGoodsMapper.updateById(updateGoods);
        if (updateCount <= 0) {
            return AjaxResult.error("商品已被抢购");
        }
        // 3. 创建订单对象
        HtOrder order = getOrder(userId, htGoods);
        if (StringUtils.isNull(order)) {
            return AjaxResult.error("订单创建失败");
        }
        // 4. 插入订单
        int orderResult = htOrderMapper.insertHtOrder(order);
        if (orderResult <= 0) {
            return AjaxResult.error("订单创建失败");
        }
        return AjaxResult.success("抢购成功");
    }
}

