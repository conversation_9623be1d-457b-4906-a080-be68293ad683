package com.ruoyi.quartz.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.enums.OrderStatusEnum;
import com.ruoyi.system.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025-08-12
 * @description: 异步处理所有抢购完成的订单
 */
@Slf4j
@Component("OrderRecordTask")
public class OrderRecordTask {

    @Autowired
    private HtGoodsMapper htGoodsMapper;

    @Autowired
    private HtOrderMapper htOrderMapper;

    @Autowired
    private HtOrderRecordMapper htOrderRecordMapper;

    @Autowired
    private AppUserMapper appUserMapper;

    //今日订单，根据算力进行收付款记录的生成
    public void computingPower() {
        if(shouldTriggerAlgorithm()){
            executeAlgorithmAsync();
        }
    }

    /**
     * 检查是否需要触发算法
     */
    private boolean shouldTriggerAlgorithm() {
        List<HtGoods> goodsList = htGoodsMapper.selectList(Wrappers.<HtGoods>query()
                .eq("is_show", 1)
                .eq("is_del", 0));
        return goodsList.isEmpty();
    }
    /**
     * 执行算法
     */
    @Transactional
    public void executeAlgorithmAsync() {
        try {
            log.info("开始执行算力匹配算法");

            // 查询今天所有待处理的付款订单
            List<HtOrder> orderList = htOrderMapper.selectList(Wrappers.<HtOrder>query()
                    .like("created_at", DateUtils.getDate())
                    .eq("order_status", OrderStatusEnum.PENDING_REVIEW.getCode()));

            if (orderList.isEmpty()) {
                log.info("今日无待处理订单");
                return;
            }
            log.info("今日待处理付款订单数量: {}", orderList.size());

            // 执行付款优化算法，避免交叉付款
            optimizePaymentOrders(orderList);

        } catch (Exception e) {
            log.error("执行算法异常", e);
        }
    }

    /**
     * 优化付款订单算法 - 避免用户之间交叉付款
     * 规则：所有付款订单需要进行匹配，避免相互交叉付款，减少用户的付款次数
     */
    private void optimizePaymentOrders(List<HtOrder> orderList) {
        log.info("开始执行付款优化算法");

        // 首先处理原始订单中相互交叉且金额一致的情况
        processOriginalCrossPaymentOrders(orderList);

        // 按用户分组统计付款和收款金额（只统计未处理的订单）
        Map<Long, BigDecimal> userPaymentMap = new HashMap<>(); // 用户需要付款的总额
        Map<Long, BigDecimal> userReceiveMap = new HashMap<>(); // 用户需要收款的总额

        // 获取未处理的订单列表（排除已经处理过的相互交叉付款订单）
        List<HtOrder> unprocessedOrders = orderList.stream()
                .filter(order -> order.getOrderStatus().equals(OrderStatusEnum.PENDING_REVIEW.getCode()))
                .collect(Collectors.toList());

        log.info("剔除相互交叉付款订单后，剩余未处理订单数量: {}", unprocessedOrders.size());

        // 统计每个用户的付款总额（作为付款人，只统计未处理的订单）
        for (HtOrder order : unprocessedOrders) {
            userPaymentMap.merge(order.getUserId(), order.getAmount(), BigDecimal::add);
        }

        // 统计每个用户的收款总额（作为收款人，只统计未处理的订单）
        for (HtOrder order : unprocessedOrders) {
            if (order.getRecipientId() != null) {
                userReceiveMap.merge(order.getRecipientId().longValue(), order.getAmount(), BigDecimal::add);
            }
        }

        log.info("用户付款统计: {}", userPaymentMap);
        log.info("用户收款统计: {}", userReceiveMap);

        // 计算每个用户的净付款金额（付款 - 收款）
        Map<Long, BigDecimal> netPaymentMap = calculateNetPayments(userPaymentMap, userReceiveMap);

        // 处理特殊情况和生成优化后的付款记录（使用未处理的订单列表）
        processSpecialCasesAndGenerateRecords(netPaymentMap, unprocessedOrders);
    }

    /**
     * 处理原始订单中相互交叉且金额一致的情况
     * 基于原始订单金额进行判断，而不是净付款金额
     */
    private void processOriginalCrossPaymentOrders(List<HtOrder> orderList) {
        log.info("开始处理原始订单中的相互交叉付款");

        // 创建用户对之间的付款关系映射
        Map<String, BigDecimal> crossPaymentMap = new HashMap<>();

        // 遍历所有订单，建立用户间的付款关系
        for (HtOrder order : orderList) {
            if (order.getRecipientId() != null) {
                Long payerId = order.getUserId();
                Long receiverId = order.getRecipientId().longValue();

                // 创建付款关系的key（付款人->收款人）
                String paymentKey = payerId + "->" + receiverId;
                crossPaymentMap.merge(paymentKey, order.getAmount(), BigDecimal::add);
            }
        }

        log.info("原始付款关系映射: {}", crossPaymentMap);

        // 查找相互交叉且金额一致的付款对
        Set<String> processedPairs = new HashSet<>();

        for (Map.Entry<String, BigDecimal> entry : crossPaymentMap.entrySet()) {
            String paymentKey = entry.getKey();
            BigDecimal amount = entry.getValue();

            // 解析付款关系
            String[] parts = paymentKey.split("->");
            if (parts.length != 2) continue;

            Long payerId = Long.valueOf(parts[0]);
            Long receiverId = Long.valueOf(parts[1]);

            // 构造反向付款关系的key
            String reverseKey = receiverId + "->" + payerId;

            // 检查是否存在反向付款且金额相等
            if (crossPaymentMap.containsKey(reverseKey) &&
                crossPaymentMap.get(reverseKey).compareTo(amount) == 0) {

                // 避免重复处理同一对用户
                String pairKey = Math.min(payerId, receiverId) + "<->" + Math.max(payerId, receiverId);
                if (processedPairs.contains(pairKey)) {
                    continue;
                }
                processedPairs.add(pairKey);

                log.info("发现原始订单相互交叉付款金额一致: 用户{} <-> 用户{}, 金额={}",
                        payerId, receiverId, amount);

                // 更新相关订单状态为已确认收款
                updateCrossPaymentOrderStatuses(payerId, receiverId, amount, orderList);
            }
        }
    }

    /**
     * 更新相互交叉付款的订单状态
     * @param userA 用户A的ID
     * @param userB 用户B的ID
     * @param amount 交叉付款金额
     * @param orderList 订单列表
     */
    private void updateCrossPaymentOrderStatuses(Long userA, Long userB, BigDecimal amount, List<HtOrder> orderList) {
        // 更新用户A向用户B付款的订单（只处理状态为待审核且金额匹配的订单）
        List<HtOrder> aToB = orderList.stream()
                .filter(order -> order.getUserId().equals(userA) &&
                        order.getRecipientId() != null &&
                        order.getRecipientId().equals(userB.intValue()) &&
                        order.getAmount().compareTo(amount) == 0 &&
                        order.getOrderStatus().equals(OrderStatusEnum.PENDING_REVIEW.getCode()))
                .collect(Collectors.toList());

        for (HtOrder order : aToB) {
            order.setOrderStatus(OrderStatusEnum.CONFIRMED_PENDING_CONSIGNMENT.getCode());
            order.setUpdatedAt(new Date());
            order.setMatchedTime(new Date());
            order.setPaymentTime(new Date());
            htOrderMapper.updateById(order);

            log.info("原始交叉付款订单处理完成: 订单ID={}, 用户{}向用户{}付款, 金额={}, 状态={}",
                    order.getId(), userA, userB, amount, OrderStatusEnum.CONFIRMED_PENDING_CONSIGNMENT.getDescription());
        }

        // 更新用户B向用户A付款的订单（只处理状态为待审核且金额匹配的订单）
        List<HtOrder> bToA = orderList.stream()
                .filter(order -> order.getUserId().equals(userB) &&
                        order.getRecipientId() != null &&
                        order.getRecipientId().equals(userA.intValue()) &&
                        order.getAmount().compareTo(amount) == 0 &&
                        order.getOrderStatus().equals(OrderStatusEnum.PENDING_REVIEW.getCode()))
                .collect(Collectors.toList());

        for (HtOrder order : bToA) {
            order.setOrderStatus(OrderStatusEnum.CONFIRMED_PENDING_CONSIGNMENT.getCode());
            order.setUpdatedAt(new Date());
            order.setMatchedTime(new Date());
            order.setPaymentTime(new Date());
            htOrderMapper.updateById(order);

            log.info("原始交叉付款订单处理完成: 订单ID={}, 用户{}向用户{}付款, 金额={}, 状态={}",
                    order.getId(), userB, userA, amount, OrderStatusEnum.CONFIRMED_PENDING_CONSIGNMENT.getDescription());
        }
    }

    /**
     * 计算每个用户的净付款金额
     */
    private Map<Long, BigDecimal> calculateNetPayments(Map<Long, BigDecimal> userPaymentMap,
                                                       Map<Long, BigDecimal> userReceiveMap) {
        Map<Long, BigDecimal> netPaymentMap = new HashMap<>();

        // 所有涉及的用户ID
        Set<Long> allUserIds = new HashSet<>();
        allUserIds.addAll(userPaymentMap.keySet());
        allUserIds.addAll(userReceiveMap.keySet());

        for (Long userId : allUserIds) {
            BigDecimal payment = userPaymentMap.getOrDefault(userId, BigDecimal.ZERO);
            BigDecimal receive = userReceiveMap.getOrDefault(userId, BigDecimal.ZERO);
            BigDecimal netPayment = payment.subtract(receive);
            netPaymentMap.put(userId, netPayment);
        }

        log.info("用户净付款统计: {}", netPaymentMap);
        return netPaymentMap;
    }

    /**
     * 处理特殊情况和生成优化后的付款记录
     */
    private void processSpecialCasesAndGenerateRecords(Map<Long, BigDecimal> netPaymentMap, List<HtOrder> unprocessedOrders) {
        // 分离不同类型的用户
        List<Long> netReceiversUsers = new ArrayList<>(); // 收款的用户主键
        List<Map.Entry<Long, BigDecimal>> netPayers = new ArrayList<>(); // 净付款用户数据
        List<Map.Entry<Long, BigDecimal>> netReceivers = new ArrayList<>(); // 净收款用户数据


        for (Map.Entry<Long, BigDecimal> entry : netPaymentMap.entrySet()) {
            BigDecimal netAmount = entry.getValue();
            if (netAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 需要付款
                netPayers.add(entry);
            } else if (netAmount.compareTo(BigDecimal.ZERO) < 0) {
                // 收款的用户
                netReceiversUsers.add(entry.getKey());
                // 需要收款
                netReceivers.add(new AbstractMap.SimpleEntry<>(entry.getKey(), netAmount.abs()));
            }
        }

        log.info("净付款用户数量: {}, 净收款用户数量: {}",netPayers.size(), netReceivers.size());

        // 处理只需收款的订单（使用未处理的订单列表）
        processNoPaymentOrders(netReceiversUsers, unprocessedOrders);

        // 处理剩余的付款匹配（使用未处理的订单列表）
        if (!netPayers.isEmpty() && !netReceivers.isEmpty()) {
            executeOptimalMatching(netPayers, netReceivers, unprocessedOrders);
        }
        // 更新剩余订单状态（使用未处理的订单列表）
        updateRemainingOrderStatuses(unprocessedOrders);
    }

    /**
     * 处理无需付款的订单
     * 如果用户净付款金额为0，直接修改订单状态为"订单已支付 待确认收款"
     */
    private void processNoPaymentOrders(List<Long> noPaymentUsers, List<HtOrder> unprocessedOrders) {
        for (Long userId : noPaymentUsers) {
            List<HtOrder> userOrders = unprocessedOrders.stream()
                    .filter(order -> order.getUserId().equals(userId) &&
                            order.getOrderStatus().equals(OrderStatusEnum.PENDING_REVIEW.getCode()))
                    .collect(Collectors.toList());

            for (HtOrder order : userOrders) {
                order.setOrderStatus(OrderStatusEnum.PAID_PENDING_CONFIRMATION.getCode());
                order.setUpdatedAt(new Date());
                order.setMatchedTime(new Date());
                order.setPaymentTime(new Date()); // 设置付款时间
                htOrderMapper.updateById(order);

                log.info("无需付款订单处理完成: 订单ID={}, 用户ID={}, 状态={}",
                        order.getId(), userId, OrderStatusEnum.PAID_PENDING_CONFIRMATION.getDescription());
            }
        }
    }

    /**
     * 执行最优匹配算法
     */
    private void executeOptimalMatching(List<Map.Entry<Long, BigDecimal>> netPayers,
                                        List<Map.Entry<Long, BigDecimal>> netReceivers,
                                        List<HtOrder> unprocessedOrders) {

        int payerIndex = 0;
        int receiverIndex = 0;

        while (payerIndex < netPayers.size() && receiverIndex < netReceivers.size()) {
            Map.Entry<Long, BigDecimal> payer = netPayers.get(payerIndex);
            Map.Entry<Long, BigDecimal> receiver = netReceivers.get(receiverIndex);

            Long payerUserId = payer.getKey();
            Long receiverUserId = receiver.getKey();
            BigDecimal payerAmount = payer.getValue();
            BigDecimal receiverAmount = receiver.getValue();

            // 计算实际转账金额（取较小值）
            BigDecimal transferAmount = payerAmount.min(receiverAmount);

            log.info("匹配付款: 用户{} 向用户{} 付款 {}", payerUserId, receiverUserId, transferAmount);

            // 为匹配的付款用户和收款用户创建对应的订单记录
            createOrderRecordsForMatching(payerUserId, receiverUserId, transferAmount, unprocessedOrders);

            // 更新剩余金额
            BigDecimal remainingPayerAmount = payerAmount.subtract(transferAmount);
            BigDecimal remainingReceiverAmount = receiverAmount.subtract(transferAmount);

            if (remainingPayerAmount.compareTo(BigDecimal.ZERO) == 0) {
                payerIndex++;
            } else {
                payer.setValue(remainingPayerAmount);
            }

            if (remainingReceiverAmount.compareTo(BigDecimal.ZERO) == 0) {
                receiverIndex++;
            } else {
                receiver.setValue(remainingReceiverAmount);
            }
        }
    }



    /**
     * 更新剩余订单状态
     */
    private void updateRemainingOrderStatuses(List<HtOrder> unprocessedOrders) {
        for (HtOrder order : unprocessedOrders) {
            // 只更新状态仍为待审核的订单
            if (OrderStatusEnum.PENDING_REVIEW.getCode().equals(order.getOrderStatus())) {
                order.setOrderStatus(OrderStatusEnum.ALLOCATION_COMPLETED.getCode());
                order.setUpdatedAt(new Date());
                order.setMatchedTime(new Date());
                htOrderMapper.updateById(order);

                log.info("更新剩余订单状态: 订单ID={}, 状态={}",
                        order.getId(), OrderStatusEnum.ALLOCATION_COMPLETED.getDescription());
            }
        }
    }



    /**
     * 为匹配的付款创建对应的订单记录
     */
    private void createOrderRecordsForMatching(Long payerUserId, Long receiverUserId, BigDecimal transferAmount, List<HtOrder> unprocessedOrders) {
        try {
            // 获取收款用户信息
            AppUser receiverUser = appUserMapper.selectById(receiverUserId);

            if (receiverUser == null) {
                log.error("收款用户信息不存在: 收款用户ID={}", receiverUserId);
                return;
            }

            // 获取付款用户的所有待处理订单，为每个订单创建对应的订单记录
            List<HtOrder> payerOrders = unprocessedOrders.stream()
                    .filter(order -> order.getUserId().equals(payerUserId) &&
                            order.getOrderStatus().equals(OrderStatusEnum.PENDING_REVIEW.getCode()))
                    .collect(Collectors.toList());

            log.info("为付款用户{}的{}个订单创建订单记录，收款用户={}, 转账金额={}",
                    payerUserId, payerOrders.size(), receiverUserId, transferAmount);

            // 为付款用户的每个订单创建对应的订单记录
            for (HtOrder payerOrder : payerOrders) {
                createSingleOrderRecord(payerOrder, receiverUser, transferAmount);
            }

        } catch (Exception e) {
            log.error("创建匹配订单记录异常: 付款用户ID={}, 收款用户ID={}, 金额={}",
                    payerUserId, receiverUserId, transferAmount, e);
        }
    }

    /**
     * 为单个订单创建订单记录
     * 确保orderId与HtOrder的id一一对应
     */
    private void createSingleOrderRecord(HtOrder order, AppUser receiverUser, BigDecimal amount) {
        try {
            HtOrderRecord orderRecord = new HtOrderRecord();
            orderRecord.setOrderId(order.getId().intValue()); // 确保orderId与订单ID一一对应
            orderRecord.setUserId(order.getUserId().intValue());
            orderRecord.setRecipientId(receiverUser.getId().intValue());
            orderRecord.setRecipientName(receiverUser.getRealName());
            orderRecord.setRecipientPhone(receiverUser.getPhone());
            orderRecord.setAmount(amount);
            orderRecord.setType(0); // 0未确认
            orderRecord.setCreateTime(new Date());

            htOrderRecordMapper.insert(orderRecord);

            log.info("创建订单记录成功: 订单ID={}, 付款用户ID={}, 收款用户={}, 金额={}",
                    order.getId(), order.getUserId(), receiverUser.getRealName(), amount);

        } catch (Exception e) {
            log.error("创建订单记录异常: 订单ID={}, 收款用户ID={}, 金额={}",
                    order.getId(), receiverUser.getId(), amount, e);
        }
    }




}